import React, { useEffect, useState, useContext } from "react";
import DefaultImageProfile from "../assets/avatar.png";
import { AuthContext } from "../contexts/AuthContext";
import axios from "axios";
import { FaRegCommentDots, FaRegNewspaper } from "react-icons/fa";

function Profile() {
  const [userProfile, setUserProfile] = useState(null);
  const { user, baseUrl } = useContext(AuthContext);

  useEffect(() => {
    if (user?.id) {
      axios
        .get(`${baseUrl}/users/${user.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        })
        .then((response) => {
          setUserProfile(response.data.data);
        })
        .catch((error) => {
          console.error("Error fetching user profile:", error);
        });
    }
  }, [user?.id, baseUrl]);

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-red-100 text-gray-800 text-lg font-semibold">
        Please Login First To View Profile Page.
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-red-50 flex justify-center items-center p-4">
      <div className="bg-white max-w-2xl w-full rounded-2xl shadow-xl overflow-hidden border border-gray-200">
        
        {/* الغلاف العلوي */}
        <div className="bg-gradient-to-r from-red-600 to-gray-800 h-40 relative">
          <img
            src={userProfile?.profile_image || DefaultImageProfile}
            alt="Profile"
            className="w-28 h-28 rounded-full border-4 border-white shadow-lg object-cover absolute -bottom-14 left-1/2 transform -translate-x-1/2 hover:scale-105 transition duration-300"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = DefaultImageProfile;
            }}
          />
        </div>

        {/* بيانات المستخدم */}
        <div className="mt-16 text-center px-6 pb-6">
          <h1 className="text-3xl font-bold text-gray-800">
            {userProfile?.name}
          </h1>
          <p className="text-gray-500 text-sm mt-1">
            @{userProfile?.username}
          </p>
          <p className="text-gray-500 mt-2">{userProfile?.email}</p>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-2 gap-6 px-6 pb-8">
          <div className="bg-gradient-to-tr from-gray-100 to-red-100 rounded-xl shadow-md p-5 text-center hover:shadow-lg hover:scale-105 transition duration-300">
            <FaRegNewspaper className="mx-auto text-red-600 text-2xl mb-2" />
            <p className="text-2xl font-bold text-gray-800">
              {userProfile?.posts_count || 0}
            </p>
            <p className="text-sm text-gray-600">Posts</p>
          </div>
          <div className="bg-gradient-to-tr from-gray-100 to-red-200 rounded-xl shadow-md p-5 text-center hover:shadow-lg hover:scale-105 transition duration-300">
            <FaRegCommentDots className="mx-auto text-red-700 text-2xl mb-2" />
            <p className="text-2xl font-bold text-gray-800">
              {userProfile?.comments_count || 0}
            </p>
            <p className="text-sm text-gray-600">Comments</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Profile;
